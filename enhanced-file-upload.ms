# Background

I want to improve the UI/UX by separating file uploads from message creation in the Salmate application. Currently, files are uploaded during message submission in the `PlatformConversationView.post()` method, which can cause delays and poor user experience.

**Desired Architecture:**
1. **Immediate Upload**: When users select files in the frontend, trigger an immediate upload to Azure Blob Storage for the specific `platform_identity` and `ticket`
2. **New Upload Endpoint**: A dedicated API endpoint that handles file uploads independently
3. **Return Metadata**: The upload endpoint should return file URLs and metadata that can be stored temporarily in the frontend
4. **Modified Message Creation**: Accept pre-uploaded file URLs and metadata instead of raw file contents, eliminating the need for additional uploads during message creation


# API Endpoints Overview

## Upload Endpoint
URL: POST /api/customers/{customer_id}/platforms/{platform_id}/upload-files/
Content-Type: multipart/form-data
Authentication: None (matches existing message endpoint)
File Keys: Files must have keys starting with file_ (e.g., file_0, file_1)
Success Upload Response:
{
  "success": true,
  "ticket_id": 123,
  "uploaded_files": [
    {
      "file_id": "550e8400-e29b-41d4-a716-446655440000",
      "original_name": "document.pdf",
      "url": "https://storage.blob.core.windows.net/container/user/emp123/messages/customer_456/ticket_789/20240115_143022_document.pdf?sv=2022-11-02&ss=b&srt=sco&sp=r&se=2025-01-15T14:30:22Z&st=2024-01-15T14:30:22Z&spr=https&sig=...",
      "metadata": {
        "name": "document.pdf",
        "size": 1024000,
        "type": "application/pdf",
        "uploaded_at": "2024-01-15T14:30:22.123456Z",
        "blob_path": "user/emp123/messages/customer_456/ticket_789/20240115_143022_document.pdf"
      }
    }
  ],
  "failed_files": [],
  "summary": {
    "total_attempted": 1,
    "successful": 1,
    "failed": 0,
    "total_size": 1024000
  }
}

Partial Upload Reponse:
{
  "success": true,
  "ticket_id": 123,
  "uploaded_files": [
    {
      "file_id": "550e8400-e29b-41d4-a716-446655440001",
      "original_name": "image.jpg",
      "url": "https://storage.blob.core.windows.net/container/user/emp123/messages/customer_456/ticket_789/20240115_143025_image.jpg?sv=...",
      "metadata": {
        "name": "image.jpg",
        "size": 512000,
        "type": "image/jpeg",
        "uploaded_at": "2024-01-15T14:30:25.789012Z",
        "blob_path": "user/emp123/messages/customer_456/ticket_789/20240115_143025_image.jpg"
      }
    }
  ],
  "failed_files": [
    {
      "original_name": "malware.exe",
      "size": 2048000,
      "error": "File type .exe is not allowed"
    }
  ],
  "summary": {
    "total_attempted": 2,
    "successful": 1,
    "failed": 1,
    "total_size": 512000
  }
}

## Deletion Endpoint
URL: DELETE /api/customers/{customer_id}/platforms/{platform_id}/files/
Content-Type: application/json
Authentication: None (matches upload endpoint)
Body: {"blob_path": "user/emp123/messages/customer_456/ticket_789/20240115_143022_document.pdf"}
Successful Deletion Response:
{
  "success": true,
  "message": "File deleted successfully",
  "blob_path": "user/emp123/messages/customer_456/ticket_789/20240115_143022_document.pdf"
}
Failed Deletion Response:
{
  "success": false,
  "error": "File deletion failed. File may not exist or may have already been deleted",
  "blob_path": "user/emp123/messages/customer_456/ticket_789/20240115_143022_document.pdf"
}


## Message Creation Endpoint (Updated)
URL: POST /api/customers/{customer_id}/platforms/{platform_id}/messages/
Content-Type: application/json
New Field: pre_uploaded_files array containing file metadata from upload response