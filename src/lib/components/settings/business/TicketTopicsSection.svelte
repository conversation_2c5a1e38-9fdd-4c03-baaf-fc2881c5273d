<!-- $/src/lib/components/settings/business/TicketTopicsSection.svelte -->
<script lang="ts">
    import { t } from '$src/lib/stores/i18n';
    import { toastStore } from '$lib/stores/toastStore';
    import { List, Li, Heading, Button, Input, Label, Modal, Card, Badge, Alert, Select, Checkbox, Dropdown, DropdownItem, Table, TableHead, TableBody, TableBodyCell, TableBodyRow, TableHeadCell, Tooltip } from 'flowbite-svelte';
    import { AdjustmentsHorizontalSolid, CirclePlusSolid, EditOutline, TrashBinSolid, CaretDownSolid, CaretUpSolid, InfoCircleSolid, EyeSolid, EyeSlashSolid, EyeSlashOutline, ChevronDownOutline } from 'flowbite-svelte-icons';
    import { services } from '$lib/api/features';
    import type { TicketTopic } from '$lib/api/types/ticket';
    import { displayDate } from '$lib/utils';

    export let ticketTopics: TicketTopic[] = [];
    export let access_token: string;
    export let user_id: number;
    const ticketsService = services.tickets;

    // Component state
    let isLoading = false;
    let showEditModal = false;
    let showDeleteModal = false;
    let showCreateModal = false;
    let showDuplicateModal = false;
    let selectedTopic: TicketTopic | null = null;
    let duplicateInfo: any = null;

    // Filter state
    let statusFilter = 'active'; // 'all', 'active', 'inactive'

    // Sorting state
    let sortColumn = 'case_type'; // 'case_type', 'case_topic', 'updated_on'
    let sortDirection = 'asc'; // 'asc', 'desc'

    // Form data
    let formData = {
        case_type: '',
        case_topic: '',
        description: '',
        is_active: true
    };

    // Form state for case type selection
    let showOtherCaseType = false;
    let customCaseType = '';

    // Reactive computations for filtered topics
    $: filteredTopics = ticketTopics.filter(topic => {
        if (statusFilter === 'active') return topic.is_active;
        if (statusFilter === 'inactive') return !topic.is_active;
        return true; // 'all'
    });

    // Get unique case types for dropdown
    $: uniqueCaseTypes = [...new Set(ticketTopics.map(topic => topic.case_type))].sort();

    // Sort topics based on current sort column and direction
    $: sortedTopics = [...filteredTopics].sort((a, b) => {
        let comparison = 0;
        
        switch (sortColumn) {
            case 'case_type':
                comparison = a.case_type.localeCompare(b.case_type);
                // Secondary sort by case_topic when case_type is primary
                if (comparison === 0) {
                    comparison = a.case_topic.localeCompare(b.case_topic);
                }
                break;
            case 'case_topic':
                comparison = a.case_topic.localeCompare(b.case_topic);
                break;
            case 'updated_on':
                comparison = new Date(a.updated_on).getTime() - new Date(b.updated_on).getTime();
                break;
            default:
                comparison = a.case_type.localeCompare(b.case_type);
                if (comparison === 0) {
                    comparison = a.case_topic.localeCompare(b.case_topic);
                }
        }
        
        return sortDirection === 'asc' ? comparison : -comparison;
    });

    // Sorting function
    function handleSort(column: string) {
        if (sortColumn === column) {
            // Toggle direction if same column
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // New column, default to ascending
            sortColumn = column;
            sortDirection = 'asc';
        }
    }

    // Handle case type selection
    function handleCaseTypeChange(value: string) {
        if (value === 'other') {
            showOtherCaseType = true;
            formData.case_type = '';
        } else {
            showOtherCaseType = false;
            formData.case_type = value;
        }
    }

    // Modal functions
    // Create √ - Retrieve X - Update √ - Delete (soft) 
    function openCreateModal() {
        selectedTopic = null;
        formData = {
            case_type: '',
            case_topic: '',
            description: '',
            is_active: true
        };
        showOtherCaseType = false;
        customCaseType = '';
        showCreateModal = true;
    }

    function openEditModal(topic: TicketTopic) {
        selectedTopic = topic;
        formData = {
            case_type: topic.case_type,
            case_topic: topic.case_topic,
            description: topic.description || '',
            is_active: topic.is_active
        };
        showOtherCaseType = false;
        customCaseType = '';
        showEditModal = true;
    }

    function openDeleteModal(topic: TicketTopic) {
        selectedTopic = topic;
        showDeleteModal = true;
    }

    function closeModals() {
        showEditModal = false;
        showDeleteModal = false;
        showCreateModal = false;
        showDuplicateModal = false;
        selectedTopic = null;
        duplicateInfo = null;
        formData = { case_type: '', case_topic: '', description: '', is_active: true };
    }

    // Utility functions
    function formatDateTime(dateString: string): string {
        return new Date(dateString).toLocaleString();
    }

    // CRUD operations
    async function createTopic() {
        const finalCaseType = showOtherCaseType ? customCaseType.trim() : formData.case_type.trim();
        
        if (!finalCaseType || !formData.case_topic.trim()) {
            toastStore.add('Case type and case topic are required', 'error');
            return;
        }

        // Check for duplicate type & topic pair (case-insensitive)
        const existingTopic = ticketTopics.find(topic => 
            topic.case_type.toLowerCase() === finalCaseType.toLowerCase() && 
            topic.case_topic.toLowerCase() === formData.case_topic.trim().toLowerCase()
        );

        if (existingTopic) {
            // Check if this is an exact match
            const isExactMatch = existingTopic.case_type === finalCaseType && 
                                   existingTopic.case_topic === formData.case_topic.trim();
            
            // Show duplicate handling modal
            duplicateInfo = {
                existing: existingTopic,
                new: {
                    case_type: finalCaseType,
                    case_topic: formData.case_topic.trim(),
                    description: formData.description.trim()
                },
                isExactMatch: isExactMatch
            };
            showDuplicateModal = true;
            return;
        }

        isLoading = true;
        try {
            const result = await ticketsService.createTicketTopic(access_token, {
                case_type: finalCaseType,
                case_topic: formData.case_topic.trim(),
                description: formData.description.trim() || undefined,
                is_active: formData.is_active,
                created_by_id: user_id
            });

            if (result.res_status === 201 && result.topic) {
                ticketTopics = [...ticketTopics, result.topic];
                closeModals();
                toastStore.add('Topic created successfully', 'success');
            } else {
                closeModals();
                toastStore.add(result.error_msg || 'Failed to create topic', 'error');
            }
        } catch (error) {
            closeModals();
            toastStore.add('Failed to create topic', 'error');
        } finally {
            isLoading = false;
        }
    }

    async function updateTopic() {
        const finalCaseType = showOtherCaseType ? customCaseType.trim() : formData.case_type.trim();
        
        if (!finalCaseType || !formData.case_topic.trim()) {
            toastStore.add('Case type and case topic are required', 'error');
            return;
        }

        // Check for duplicate type & topic pair (case-insensitive)
        const existingTopic = ticketTopics.find(topic => 
            topic.id !== selectedTopic.id &&
            topic.case_type.toLowerCase() === finalCaseType.toLowerCase() && 
            topic.case_topic.toLowerCase() === formData.case_topic.trim().toLowerCase()
        );

        if (existingTopic) {
            // Check if this is an exact match
            const isExactMatch = existingTopic.case_type === finalCaseType && 
                                   existingTopic.case_topic === formData.case_topic.trim();
            
            // Show duplicate handling modal
            duplicateInfo = {
                existing: existingTopic,
                new: {
                    case_type: finalCaseType,
                    case_topic: formData.case_topic.trim(),
                    description: formData.description.trim()
                },
                isUpdate: true,
                currentTopic: selectedTopic,
                isExactMatch: isExactMatch
            };
            showDuplicateModal = true;
            return;
        }

        isLoading = true;
        try {
            const result = await ticketsService.updateTicketTopic(access_token, selectedTopic.id, {
                case_type: finalCaseType,
                case_topic: formData.case_topic.trim(),
                description: formData.description.trim() || undefined,
                is_active: formData.is_active,
                updated_by_id: user_id
            });

            if (result.res_status === 200 && result.topic) {
                ticketTopics = ticketTopics.map(topic => 
                    topic.id === selectedTopic.id ? result.topic : topic
                );
                closeModals();
                toastStore.add('Topic updated successfully', 'success');
            } else {
                closeModals();
                toastStore.add(result.error_msg || 'Failed to update topic', 'error');
            }
        } catch (error) {
            closeModals();
            toastStore.add('Failed to update topic', 'error');
        } finally {
            isLoading = false;
        }
    }

    // Soft delete
    async function toggleTopicStatus() {
        if (!selectedTopic) return;
        
        isLoading = true;
        const newStatus = !selectedTopic.is_active;
        const actionText = newStatus ? 'reactivated' : 'deactivated';
        
        try {
            // Toggle is_active status using update endpoint
            const result = await ticketsService.updateTicketTopic(access_token, selectedTopic.id, {
                is_active: newStatus,
                updated_by_id: user_id
            });

            if (result.res_status === 200 && result.topic) {
                ticketTopics = ticketTopics.map(topic => 
                    topic.id === selectedTopic.id ? result.topic : topic
                );
                closeModals();
                toastStore.add(`Topic ${actionText} successfully`, 'success');
            } else {
                closeModals();
                toastStore.add(result.error_msg || `Failed to ${actionText.slice(0, -1)} topic`, 'error');
            }
        } catch (error) {
            closeModals();
            toastStore.add(`Failed to ${actionText.slice(0, -1)} topic`, 'error');
        } finally {
            isLoading = false;
        }
    }

    // Handle duplicate topic resolution
    // With the new modal, only action = 'rename_new' and 'proceed_anyway' are in use
    async function handleDuplicateResolution(action: 'use_existing' | 'reactivate_existing' | 'rename_old' | 'rename_new' | 'proceed_anyway') {
        if (!duplicateInfo) return;

        if (action === 'use_existing') {
            // Just close the modals - no notification needed 
            closeModals();
        } else if (action === 'reactivate_existing') {
            // Reactivate the existing inactive topic
            isLoading = true;
            try {
                const result = await ticketsService.updateTicketTopic(access_token, duplicateInfo.existing.id, {
                    is_active: true,
                    updated_by_id: user_id
                });

                if (result.res_status === 200 && result.topic) {
                    ticketTopics = ticketTopics.map(topic => 
                        topic.id === duplicateInfo.existing.id ? result.topic : topic
                    );
                    closeModals();
                    toastStore.add(`Topic "${duplicateInfo.existing.case_topic}" has been reactivated and is now available for use.`, 'success');
                } else {
                    closeModals();
                    toastStore.add(result.error_msg || 'Failed to reactivate topic', 'error');
                }
            } catch (error) {
                closeModals();
                toastStore.add('Failed to reactivate topic', 'error');
            } finally {
                isLoading = false;
            }
        } else if (action === 'rename_old') {
            // Open edit modal for the existing topic
            closeModals();
            setTimeout(() => {
                openEditModal(duplicateInfo.existing);
            }, 100);
        } else if (action === 'rename_new') {
            // Go back to create/edit modal with current data
            showDuplicateModal = false;
            if (duplicateInfo.isUpdate) {
                showEditModal = true;
            } else {
                showCreateModal = true;
            }
        } else if (action === 'proceed_anyway') {
            // Create/update the topic anyway
            isLoading = true;
            try {
                if (duplicateInfo.isUpdate) {
                    // Update existing topic
                    const result = await ticketsService.updateTicketTopic(access_token, duplicateInfo.currentTopic.id, {
                        case_type: duplicateInfo.new.case_type,
                        case_topic: duplicateInfo.new.case_topic,
                        description: duplicateInfo.new.description || undefined,
                        is_active: duplicateInfo.currentTopic.is_active,
                        updated_by_id: user_id
                    });

                    if (result.res_status === 200 && result.topic) {
                        ticketTopics = ticketTopics.map(topic => 
                            topic.id === duplicateInfo.currentTopic.id ? result.topic : topic
                        );
                        closeModals();
                        toastStore.add('Topic updated successfully', 'success');
                    } else {
                        closeModals();
                        toastStore.add(result.error_msg || 'Failed to update topic', 'error');
                    }
                } else {
                    // Create new topic
                    const result = await ticketsService.createTicketTopic(access_token, {
                        case_type: duplicateInfo.new.case_type,
                        case_topic: duplicateInfo.new.case_topic,
                        description: duplicateInfo.new.description || undefined,
                        is_active: true,
                        created_by_id: user_id
                    });

                    if (result.res_status === 201 && result.topic) {
                        ticketTopics = [...ticketTopics, result.topic];
                        closeModals();
                        toastStore.add('Topic created successfully', 'success');
                    } else {
                        closeModals();
                        toastStore.add(result.error_msg || 'Failed to create topic', 'error');
                    }
                }
            } catch (error) {
                closeModals();
                toastStore.add(`Failed to ${duplicateInfo.isUpdate ? 'update' : 'create'} topic`, 'error');
            } finally {
                isLoading = false;
            }
        }
    }

    const statusesName = ['all', 'active', 'inactive'];
    
</script>

<div class="space-y-6">
    <!-- Section Header -->
    <div class="border-b border-gray-200 pb-4 mt-4">
        <h3 class="text-lg font-medium text-gray-900">{t('settings.team.ticketTopic.title')}</h3>
        <p class="text-sm text-gray-600">{t('settings.team.ticketTopic.description')}</p>
    </div>

    <!-- Controls Bar -->
    <div class="flex items-center justify-between gap-4 mb-6">
        <!-- Status Filter -->
        <div id="status-filter" data-testid="status-filter">
            <Button 
                id="status-filter-button"
                color={statusFilter === 'active' ? 'none' : 'dark'} 
                class={`${statusFilter === 'active' ? 'hover:bg-gray-100 border' : ''} shadow-md`} 
                data-testid="status-filter-button"
            >
                <AdjustmentsHorizontalSolid class="w-4 h-4 mr-1" />
                <span>{t('status')}</span>
                <ChevronDownOutline class="w-3 h-3" />
            </Button>
            <Dropdown id="status-dropdown" class="w-44 p-2 shadow-lg" data-testid="status-dropdown">
                {#each statusesName as statusName}
                    <Label id="status-option-{statusName.toLowerCase()}" class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100" data-testid="status-option">
                        <Checkbox
                            id="status-checkbox-{statusName.toLowerCase()}"
                            checked={statusFilter === statusName}
                            on:change={() => statusFilter = statusName}
                            data-testid="status-checkbox-{statusName.toLowerCase()}"
                            class="text-gray-700 focus:ring-gray-700 p-2"
                            inline
                        />
                        <span class="ml-2 text-sm">
                            {#if statusName === 'All'}
                                {t('filter_all')}
                            {:else}
                                {t('ttp.status_' + statusName)}  <!-- ttp: ticket_tickettopic -->
                            {/if}
                        </span>
                    </Label>
                {/each}
            </Dropdown>
        </div>

        <!-- Add New Button -->
        <Button color="dark" class="shadow-md" on:click={openCreateModal}>
            <CirclePlusSolid class="h-4 w-4 mr-2" />
            {t('settings.team.ticketTopic.addNewTopic')}
        </Button>
    </div>

    <!-- Topics Table -->
    <Table shadow class="table-fixed">
        <TableHead>
            <TableHeadCell class="w-[40px] cursor-pointer" on:click={() => handleSort('case_type')}>
                <div class="flex items-center justify-start">
                    {t('case_type')}
                    {#if sortColumn === 'case_type'}
                        {#if sortDirection === 'asc'}
                            <CaretUpSolid class="inline-block h-4 w-4 ml-1" />
                        {:else}
                            <CaretDownSolid class="inline-block h-4 w-4 ml-1" />
                        {/if}
                    {/if}
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-[60px] cursor-pointer" on:click={() => handleSort('case_topic')}>
                <div class="flex items-center justify-start">
                    {t('case_topic')}
                    {#if sortColumn === 'case_topic'}
                        {#if sortDirection === 'asc'}
                            <CaretUpSolid class="inline-block h-4 w-4 ml-1" />
                        {:else}
                            <CaretDownSolid class="inline-block h-4 w-4 ml-1" />
                        {/if}
                    {/if}
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-[120px]">
                <div class="flex items-center justify-start">
                    {t('description')}
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-[20px] text-center">
                <div class="flex items-center justify-center">
                    {t('status')}
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-[20px] cursor-pointer" on:click={() => handleSort('updated_on')}>
                <div class="flex items-center justify-start">
                    {t('table_updated_at')}
                    {#if sortColumn === 'updated_on'}
                        {#if sortDirection === 'asc'}
                            <CaretUpSolid class="inline-block h-4 w-4 ml-1" />
                        {:else}
                            <CaretDownSolid class="inline-block h-4 w-4 ml-1" />
                        {/if}
                    {/if}
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-[20px] text-center">{t('table_actions')}</TableHeadCell>
        </TableHead>
        
        <TableBody tableBodyClass="divide-y">
            {#if sortedTopics.length === 0}
                <TableBodyRow>
                    <TableBodyCell colspan={6} class="text-center py-4 text-gray-500">
                        <div class="text-gray-500">
                            <p class="mb-3">
                                {statusFilter === 'active' ? 'No active topics found.' : 
                                 statusFilter === 'inactive' ? 'No inactive topics found.' : 
                                 'No topics found.'}
                            </p>
                        </div>
                    </TableBodyCell>
                </TableBodyRow>
            {:else}
                {#each sortedTopics as topic}
                    <TableBodyRow class="hover:bg-gray-50">
                        <TableBodyCell class="overflow-hidden">
                            <div class="text-sm font-medium truncate" id="case-type-{topic.id}">{topic.case_type}</div>
                            <Tooltip triggeredBy="#case-type-{topic.id}">{topic.case_type}</Tooltip>
                        </TableBodyCell>
                        <TableBodyCell class="overflow-hidden">
                            <div class="text-sm font-medium truncate" id="case-topic-{topic.id}">{topic.case_topic}</div>
                            <Tooltip triggeredBy="#case-topic-{topic.id}">{topic.case_topic}</Tooltip>
                        </TableBodyCell>
                        <TableBodyCell class="overflow-hidden w-64">
                            <div class="text-xs text-gray-500 leading-tight line-clamp-2 max-h-10 break-words" id="description-{topic.id}">
                                {topic.description || '-'}
                            </div>
                            <Tooltip triggeredBy="#description-{topic.id}">{topic.description || t('no_description')}</Tooltip>
                        </TableBodyCell>
                        <TableBodyCell class="text-center">
                            <div class="flex justify-center">
                                <Badge color={topic.is_active ? 'green' : 'red'} class="text-xs font-medium">
                                    {#if topic.is_active}
                                        <EyeSolid class="w-3 h-3 mr-1" />
                                        {t('active')}
                                    {:else}
                                        <EyeSlashSolid class="w-3 h-3 mr-1" />
                                        {t('inactive')}
                                    {/if}
                                </Badge>
                            </div>
                        </TableBodyCell>
                        <TableBodyCell class="overflow-hidden">
                            <span class="text-sm font-medium truncate block">{displayDate(topic.updated_on).date}</span>
                            <span class="text-xs text-gray-500 truncate block">{displayDate(topic.updated_on).time}</span>
                        </TableBodyCell>
                        <TableBodyCell class="text-center">
                            <div class="flex items-center gap-2 justify-center">
                                <button
                                    id="edit-btn-{topic.id}"
                                    on:click={() => openEditModal(topic)}
                                    class="p-2 text-gray-500 hover:text-blue-600 transition-colors"
                                >
                                    <EditOutline class="h-5 w-5" />
                                </button>
                                <Tooltip triggeredBy="#edit-btn-{topic.id}">{t('settings.team.ticketTopic.editTopic')}</Tooltip>
                                {#if topic.is_active}
                                    <button
                                        id="deactivate-btn-{topic.id}"
                                        on:click={() => openDeleteModal(topic)}
                                        class="p-2 text-gray-500 hover:text-red-600 transition-colors"
                                    >
                                        <EyeSlashOutline class="h-5 w-5" />
                                    </button>
                                    <Tooltip triggeredBy="#deactivate-btn-{topic.id}">{t('settings.team.ticketTopic.deactivateTopic')}</Tooltip>
                                {:else}
                                    <button
                                        id="reactivate-btn-{topic.id}"
                                        on:click={() => openDeleteModal(topic)}
                                        class="p-2 text-gray-500 hover:text-green-600 transition-colors"
                                    >
                                        <EyeSolid class="h-5 w-5" />
                                    </button>
                                    <Tooltip triggeredBy="#reactivate-btn-{topic.id}">{t('settings.team.ticketTopic.reactivateTopic')}</Tooltip>
                                {/if}
                            </div>
                        </TableBodyCell>
                    </TableBodyRow>
                {/each}
            {/if}
        </TableBody>
    </Table>
</div>



<!-- Create Topic Modal -->
<Modal bind:open={showCreateModal} title="Add New Topic" size="md">
    <form on:submit|preventDefault={createTopic}>
        <div class="space-y-4">
            <div class="space-y-2">
                <Label for="create_case_type" class="mb-2">{t('case_type')}</Label>
                <Select 
                    id="create_case_type"
                    on:change={(e) => handleCaseTypeChange(e.target.value)}
                    required
                >
                    {#each uniqueCaseTypes as caseType}
                        <option value={caseType}>{caseType}</option>
                    {/each}
                    <option value="other">{t('settings.team.ticketTopic.createOther')}</option>
                </Select>
                {#if showOtherCaseType}
                    <Input
                        id="create_custom_case_type"
                        bind:value={customCaseType}
                        placeholder={t('settings.team.ticketTopic.placeholder.caseType')}
                        required
                    />
                {/if}
            </div>
            
            
            <div>
                <Label for="create_case_topic" class="mb-2">{t('case_topic')}</Label>
                <Input
                    id="create_case_topic"
                    bind:value={formData.case_topic}
                    placeholder={t('settings.team.ticketTopic.placeholder.caseTopic')}
                    required
                />
            </div>
            
            <div>
                <Label for="create_description" class="mb-2">{t('description')}</Label>
                <textarea
                    id="create_description"
                    bind:value={formData.description}
                    placeholder={t('settings.team.ticketTopic.placeholder.description')}
                    class="w-full px-3 py-2 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 resize-none"
                    rows="3"
                ></textarea>
            </div>
        </div>
        
        <div class="flex justify-end gap-3 pt-6">
            <Button color="green" type="submit" disabled={isLoading}>
                {isLoading ? t('creating') : t('settings.team.ticketTopic.createTopic')}
            </Button>
            <Button color="alternative" on:click={closeModals}>{t('cancel')}</Button>
        </div>
    </form>
</Modal>

<!-- Edit Topic Modal -->
<Modal bind:open={showEditModal} title="Edit Topic" size="md">
    <form on:submit|preventDefault={updateTopic}>
        <div class="space-y-4">
            <div class="space-y-2">
                <Label for="edit_case_type" class="mb-2">{t('case_type')}</Label>
                <Select 
                    id="edit_case_type"
                    bind:value={formData.case_type}
                    on:change={(e) => handleCaseTypeChange(e.target.value)}
                    required
                >
                    {#each uniqueCaseTypes as caseType}
                        <option value={caseType}>{caseType}</option>
                    {/each}
                    <option value="other">{t('settings.team.ticketTopic.createOther')}</option>
                </Select>
                {#if showOtherCaseType}
                    <Input
                        id="edit_custom_case_type"
                        bind:value={customCaseType}
                        placeholder={t('settings.team.ticketTopic.placeholder.caseType')}
                        required
                    />
                {/if}
            </div>
            
            <div>
                <Label for="edit_case_topic" class="mb-2">{t('case_topic')}</Label>
                <Input
                    id="edit_case_topic"
                    bind:value={formData.case_topic}
                    placeholder={t('settings.team.ticketTopic.placeholder.caseTopic')}
                    required
                />
            </div>
            
            <div>
                <Label for="edit_description" class="mb-2">{t('description')}</Label>
                <textarea
                    id="edit_description"
                    bind:value={formData.description}
                    placeholder={t('settings.team.ticketTopic.placeholder.description')}
                    class="w-full px-3 py-2 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 resize-none"
                    rows="3"
                ></textarea>
            </div>
        </div>

        <Alert color="yellow" class="mt-4">
            <span class="text-sm">
                <InfoCircleSolid class="w-4 h-4 inline align-text-center mb-1" />
                <strong>{t('warning')}:</strong> 
                {t('settings.team.ticketTopic.editWarning')}
            </span>
        </Alert>
        
        <div class="flex justify-end gap-3 pt-6">
            <Button color="green" type="submit" disabled={isLoading}>
                {isLoading ? t('updating') : t('settings.team.ticketTopic.updateTopic')}
            </Button>
            <Button color="alternative" on:click={closeModals}>{t('cancel')}</Button>
        </div>
    </form>
</Modal>

<!-- Toggle Topic Status Modal -->
<Modal bind:open={showDeleteModal} title={selectedTopic?.is_active ? "Deactivate Topic" : "Reactivate Topic"} size="md">
    {#if selectedTopic}
        <div class="space-y-4">
            <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-10 h-10 {selectedTopic.is_active ? 'bg-red-100' : 'bg-green-100'} rounded-full flex items-center justify-center">
                    {#if selectedTopic.is_active}
                        <EyeSlashOutline class="w-5 h-5 text-red-600" />
                    {:else}
                        <EyeSolid class="w-5 h-5 text-green-600" />
                    {/if}
                </div>
                <div class="flex-1">
                    <h4 class="text-lg font-medium text-gray-900 mb-2">
                        {selectedTopic.is_active ? 'Deactivate Topic' : 'Reactivate Topic'}
                    </h4>
                    <p class="text-gray-600 mb-4">
                        {#if selectedTopic.is_active}
                            {t('settings.team.ticketTopic.deactivateTopicDescription')}
                        {:else}
                            {t('settings.team.ticketTopic.reactivateTopicDescription')}
                        {/if}
                    </p>
                </div>
            </div>
            
            <div class="rounded-lg bg-gray-50 p-4">
                <div class="text-sm space-y-1">
                    <div><strong>{t('case_type')}:</strong> {selectedTopic.case_type}</div>
                    <div><strong>{t('case_topic')}:</strong> {selectedTopic.case_topic}</div>
                    {#if selectedTopic.description}
                        <div><strong>{t('description')}:</strong> {selectedTopic.description}</div>
                    {/if}
                    <div><strong>{t('current_status')}:</strong> 
                        <Badge color={selectedTopic.is_active ? 'green' : 'red'} class="text-xs ml-1">
                            {selectedTopic.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                    </div>
                </div>
            </div>
            
            <Alert color={selectedTopic.is_active ? "yellow" : "blue"} class="mt-4">
                <span class="text-sm">
                    <InfoCircleSolid class="w-4 h-4 inline align-text-center mb-1" />
                    <strong>{t('warning')}:</strong> 
                    {#if selectedTopic.is_active}
                        {t('settings.team.ticketTopic.deactivateTopicWarning')}
                    {:else}
                        {t('settings.team.ticketTopic.reactivateTopicWarning')}
                    {/if}
                </span>
            </Alert>
        </div>
        
        <div class="flex justify-end gap-3 pt-6">
            <Button 
                color={selectedTopic.is_active ? "red" : "green"} 
                on:click={toggleTopicStatus} 
                disabled={isLoading}
            >
                {#if isLoading}
                    {selectedTopic.is_active ? t('deactivation') : t('reactivation')}
                {:else}
                    {selectedTopic.is_active ? t('settings.team.ticketTopic.deactivateTopic') : t('settings.team.ticketTopic.reactivateTopic')}
                {/if}
            </Button>
            <Button color="alternative" on:click={closeModals}>{t('cancel')}</Button>
        </div>
    {/if}
</Modal>

<!-- Duplicate Topic Handling Modal -->
<Modal bind:open={showDuplicateModal} title="Existing Topic Found" size="lg">
    {#if duplicateInfo}
        <div class="space-y-6">
            <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                    <InfoCircleSolid class="w-5 h-5 text-yellow-600" />
                </div>
                <div class="flex-1">
                    <h4 class="text-lg font-medium text-gray-900 mb-2">{t('settings.team.ticketTopic.existingTopicFound')}</h4>
                    <p class="text-gray-600">
                        {t('settings.team.ticketTopic.existingTopicFoundDescription')}
                    </p>
                </div>
            </div>

            <!-- Existing Topic Info -->
            <div class="rounded-lg bg-blue-50 border border-blue-200 p-4">
                <h5 class="font-bold text-blue-900 mb-2">{t('settings.team.ticketTopic.existingTopic')}</h5>
                <div class="text-sm text-blue-800 space-y-1">
                    <div><strong>{t('case_type')}: {duplicateInfo.existing.case_type}</strong></div>
                    <div><strong>{t('case_topic')}: {duplicateInfo.existing.case_topic}</strong></div>
                    <div><strong>{t('description')}:</strong> {duplicateInfo.existing.description || t('no_description')}</div>
                    <div><strong>{t('status')}:</strong> 
                        <Badge color={duplicateInfo.existing.is_active ? 'green' : 'red'} class="text-xs ml-1">
                            {duplicateInfo.existing.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                    </div>
                    <div><strong>{t('settings.team.ticketTopic.created')}:</strong> {formatDateTime(duplicateInfo.existing.created_on)}</div>
                    <div><strong>{t('settings.team.ticketTopic.updated')}:</strong> {formatDateTime(duplicateInfo.existing.updated_on)}</div>
                </div>
            </div>

            <!-- New Topic Info -->
            <div class="rounded-lg bg-green-50 border border-green-200 p-4">
                <h5 class="font-bold text-green-900 mb-2">
                    {duplicateInfo.isUpdate ? 
                    t('settings.team.ticketTopic.yourUpdatedTopic') : 
                    t('settings.team.ticketTopic.yourNewTopic')}
                </h5>
                <div class="text-sm text-green-800 space-y-1">
                    <div><strong>{t('case_type')}: {duplicateInfo.new.case_type}</strong></div>
                    <div><strong>{t('case_topic')}: {duplicateInfo.new.case_topic}</strong></div>
                    <div><strong>{t('description')}:</strong> {duplicateInfo.new.description || t('no_description')}</div>
                </div>
            </div>

            <!-- Suggested Actions -->
            <div class="space-y-3">
                <Heading tag="h2" class="mb-2 text-lg font-semibold text-gray-900 ml-2">{t('settings.team.ticketTopic.duplicateModalSuggestion')[0]}</Heading>
                <List tag="ul" class="space-y-1 text-gray-600 ml-4">
                    <Li>
                        {#if duplicateInfo.existing.is_active}
                            {t('settings.team.ticketTopic.duplicateModalSuggestion')[1]}
                        {:else}
                            {t('settings.team.ticketTopic.duplicateModalSuggestion')[2]}
                        {/if}
                    </Li>
                    <Li>
                        {duplicateInfo.isUpdate ? 
                        t('settings.team.ticketTopic.duplicateModalSuggestion')[3] : 
                        t('settings.team.ticketTopic.duplicateModalSuggestion')[4]}
                    </Li>
                    <Li>{t('settings.team.ticketTopic.duplicateModalSuggestion')[5]}</Li>
                    {#if !duplicateInfo.isExactMatch}
                        <Li>
                            <span class="inline font-bold text-red-600">
                                {t('proceed_anyway')}. 
                            </span>
                            {t('settings.team.ticketTopic.duplicateModalSuggestion')[6]}
                        </Li>
                    {/if}
                </List>
            </div>
        </div>
        
        <div class="flex justify-end gap-3 pt-6">
            <Button color="alternative" on:click={() => handleDuplicateResolution('rename_new')}>{t('back')}</Button>
            {#if !duplicateInfo.isExactMatch}
                <Button 
                    color="red" 
                    on:click={() => handleDuplicateResolution('proceed_anyway')}
                    disabled={isLoading}
                >
                    {#if isLoading}
                        {t('processing')}
                    {:else}
                        {t('proceed_anyway')}
                    {/if}
                </Button>
            {/if}
            <Button color="alternative" on:click={closeModals}>{t('cancel')}</Button>
        </div>
    {/if}
</Modal>

<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
    }
    
    .max-h-10 {
        max-height: 2.5rem;
    }
    
    .break-words {
        word-break: break-word;
        overflow-wrap: break-word;
    }
    textarea {
        field-sizing: content;
        min-height: 4rem; 
        resize: none;
    }
</style>
